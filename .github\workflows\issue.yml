name: Issue - Labeling
on:
  workflow_dispatch:
  issues:
      types: ['opened']
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: Renato66/auto-label@v2
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          ignore-comments: true
          labels-synonyms: '{
            "Priority: Low":["Low"],
            "Priority: Medium":["Medium"],
            "Priority: High":["High"],
            "Priority: Critical":["Critical"],
            "Area: App":["[X] App"],
            "Area: Includes":["[X] Includes"],
            "Area: Resources":["[X] Resources"],
            "Area: Routes":["[X] Routes"],
            "Area: Vendor":["[X] Vendor"],
            "Area: Root":["[X] Root"],
            "Type: Enhancement":["Enhancement"],
            "Type: Missing Content":["Missing Content"]
          }'
          default-labels: '["Status: Pending Test"]'
