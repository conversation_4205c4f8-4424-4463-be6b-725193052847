---
name: 'Cron - Stale issues and PRs'
on:
  schedule:
    - cron: '30 1 * * *'

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v6
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-issue-message: 'This issue is stale because it has been open 120 days with no activity.'
          stale-pr-message: 'This PR is stale because it has been open 45 days with no activity.'
          days-before-issue-stale: 90
          days-before-pr-stale: 30
          days-before-issue-close: -1
          days-before-pr-close: -1
