<?php
/**
 * Validator class
 *
 * @package   CanaryAAC
 * <AUTHOR> <lucas<PERSON><EMAIL>>
 * @copyright 2022 CanaryAAC
 */

namespace App\Controller\Admin;

use App\Model\Entity\Login as EntityLogin;
use App\Utils\Argon;
use App\Utils\View;
use App\Http\Request;
use App\Session\Admin\Login as SessionAdminLogin;
use App\Controller\Admin\Alert;

class Login extends Base{

    /**
     * Method responsible for returning the login page rendering
     *
     * @param Request $request
     * @param string $errorMessage
     * @return string
     */
    public static function getLogin($request, $errorMessage = null)
    {
        // Login status
        $status = !is_null($errorMessage) ? Alert::getError($errorMessage) : '';

        // Render login page and $status
        return $content = View::render('admin/login', [
            'title' => 'Login - CanaryAAC',
            'status' => $status
        ]);

        //return parent::getPanel('Login', $content, 'home');
    }

    /**
     * Method responsible for setting user login
     *
     * @param Request $request
     */
    public static function setLogin($request)
    {
        $postVars = $request->getPostVars();
        $email = $postVars['login-email'] ?? '';
        $pass = $postVars['login-password'] ?? '';

        $obAccount = EntityLogin::getLoginbyEmail($email);

        // Verify email
        if(!$obAccount instanceof EntityLogin){
            return self::getLogin($request, 'Email inválidos.');
        }

        // Password verify by sha1
        if(!Argon::checkPassword($pass, $obAccount->password, $obAccount->id)){
            return self::getLogin($request, 'Password inválidos.');
        }

        // Verify account access
        if(!($obAccount->page_access > 0)){
            return self::getLogin($request, 'Você não tem acesso.');
        }
        
        SessionAdminLogin::login($obAccount);

        $request->getRouter()->redirect('/admin');
    }

    public static function setLogout($request)
    {
        SessionAdminLogin::logout();

        $request->getRouter()->redirect('/admin/login');
    }

}