name: Report a problem/bug
description: Any issues that you have found while using the CanaryAAC website
labels: ["Type: Bug"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this issue!

  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: How critical is this?
      options:
        - Low
        - Medium
        - High
        - Critical
    validations:
      required: true

  - type: dropdown
    id: area
    attributes:
      label: Area
      multiple: true
      description: Where is the problem?
      options:
        - App
        - Includes
        - Resources
        - Routes
        - Vendor
        - Root
    validations:
      required: true

  - type: dropdown
    id: os
    attributes:
      label: What OS are you seeing the problem on?
      multiple: true
      options:
        - Linux
        - Windows
        - MacOS
    validations:
      required: true

  - type: dropdown
    id: browser
    attributes:
      label: Browser
      multiple: true
      description: What browser are you seeing the problem on?
      options:
        - Chrome
        - Firefox
        - Edge
        - Opera
        - Safari
        - Other
    validations:
      required: true

  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Describe your problem/bug and add screenshots, logs or anything that can help verifying the bug
      placeholder: Tell us what you see!
      value: "A bug happened!"
    validations:
      required: true

  - type: checkboxes
    id: terms
    attributes:
      label: Code of Conduct
      description: By submitting this issue, you agree to follow our [Code of Conduct](https://github.com/opentibiabr/canary/blob/main/CODE_OF_CONDUCT.md)
      options:
        - label: I agree to follow this project's Code of Conduct
          required: true
